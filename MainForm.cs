using System;
using System.Drawing;
using System.IO.Ports;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using System.Threading;
using NLog;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Data;
using System.Runtime.InteropServices;
using System.Text;
using System.Diagnostics.CodeAnalysis;

namespace CCTS;

public partial class MainForm : Form
{
    public static readonly Logger Logger = LogManager.GetCurrentClassLogger();
    public SerialPort serialPort = new SerialPort();
    public bool bMoniting,bLog = false;

    public MainForm()
    {
        InitializeComponent();

        // 设置ProtocalCmd的mainForm引用
        ProtocalCmd.mainForm = this;

        foreach (string portName in SysConfig.portNames)
        {
            combo_Com.Items.Add(portName);
        }
        //combo_Com.SelectedItem = SysConfig.strPort;
        if (SysConfig.strPort != null)
        {
            combo_Com.SelectedItem = SysConfig.strPort;
        }

        //this.FormClosing += MainForm_Closed;
    }

    private void btn_Configuration_Click(object sender, EventArgs e)
    {
        PopFormParametersConfiguration popFormParametersConfiguration = new PopFormParametersConfiguration(this);
        popFormParametersConfiguration.Show();
        this.Hide();
    }

    private void btn_Log_Click(object sender, EventArgs e)
    {
        PopFormParametersLog popFormParametersLog = new PopFormParametersLog(this);
        popFormParametersLog.Show();
        popFormParametersLog.timerParamMonite.Change(0, Timeout.Infinite);
        bLog = true;
        this.Hide();
    }
    private void btn_TAL_Click(object sender, EventArgs e)
    {
        PopFormTAL popFormTAL = new PopFormTAL(this);
        popFormTAL.Show();
        this.Hide();
    }

    private void label2_Click(object sender, EventArgs e)
    {
        if (!bMoniting)
        {
            SysFormComm comm = new SysFormComm(this);
            comm.ShowDialog();
            if (comm.bLink)
            {
                try
                {
                    Logger.Info("打开串口");
                    serialPort.Open();
                    if (serialPort.IsOpen)
                    {
                        btn_connect.Text = "Disconnect";
                        btn_connect.BackColor = Color.FromArgb(47, 126, 39);//绿色
                        bMoniting = true;

                        ProtocalCmd.creatFile();                   

                        byte[] cmdlink = { 0xcc, 0x03, 0xf0, 0x01, 0xfe };
                        ProtocalCmdItem cmdMoniteLink = new ProtocalCmdItem(cmdlink, false, 0);
                        cmdMoniteLink.recv = new byte[1024];
                        if (bMoniting)
                        {
                            ProtocalCmd.commBlock(cmdMoniteLink,false,1000);
                        }
                        Thread.Sleep(100);
                        readVerInfo(0);

                        if (ProtocalCmd.bLink)
                        {
                            btn_Configuration.Visible = true;
                            btn_Log.Visible = true;
                            btn_TAL.Visible = true;
                        }
                        else
                        {
                            bMoniting = false;
                            btn_connect.Text = "Connect";
                            btn_connect.BackColor = Color.FromArgb(192, 79, 21);//黄色
                            serialPort.Close();
                        }                       
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error(ex.Message);
                    Logger.Error(ex.StackTrace);
                }
            }
            else
            {
                bMoniting = false;
                btn_connect.Text = "Connect";
                btn_connect.BackColor = Color.FromArgb(192, 79, 21);//黄色
                lock (ProtocalCmd._lock)
                {
                    byte[] cmdDislink = { 0xcc, 0x03, 0xf0, 0x02, 0xfd };
                    ProtocalCmdItem cmdMoniteDislink = new ProtocalCmdItem(cmdDislink, false, 0);
                    cmdMoniteDislink.recv = new byte[1024];

                    if (bMoniting)
                    {
                        ProtocalCmd.commBlock(cmdMoniteDislink,false,1000);
                    }
                    serialPort.Close();
                    btn_Configuration.Visible = false;
                    btn_Log.Visible = false;
                    btn_TAL.Visible = false;
                }

            }
        }
    }
    private void btn_connect_Click(object sender, EventArgs e)
    {
        if (!bMoniting)
        {
            serialPort.BaudRate = SysConfig.iBps;
            serialPort.Parity = (Parity)SysConfig.iParity;
            serialPort.DataBits = SysConfig.iDatabit;
            serialPort.StopBits = (StopBits)SysConfig.iStopbit;
            serialPort.PortName = SysConfig.strPort;
            try
            {

                serialPort.Open();
                if (serialPort.IsOpen)
                {
                    btn_connect.Text = "Disconnect";
                    btn_connect.BackColor = Color.FromArgb(47, 126, 39);//绿色
                    bMoniting = true;

                    ProtocalCmd.creatFile(); 

                    byte[] cmdlink = { 0xcc, 0x03, 0xf0, 0x01, 0xfe };
                    ProtocalCmdItem cmdMoniteLink = new ProtocalCmdItem(cmdlink, false, 0);
                    cmdMoniteLink.recv = new byte[1024];

                    if (bMoniting)
                    {
                        ProtocalCmd.commBlock(cmdMoniteLink,false,1000);
                    }
                    //Thread.Sleep(100);

                    readVerInfo(0);

                    if (ProtocalCmd.bLink)
                    {
                        btn_Configuration.Visible = true;
                        btn_Log.Visible = true;
                        btn_TAL.Visible = true;
                    }
                    else
                    {
                        bMoniting = false;
                        btn_connect.Text = "Connect";
                        btn_connect.BackColor = Color.FromArgb(192, 79, 21);//黄色
                        serialPort.Close();
                    }


                }

            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message);
                Logger.Error(ex.StackTrace);
            }
        }
        else
        {
            byte[] cmdDislink = { 0xcc, 0x03, 0xf0, 0x02, 0xfd };
            ProtocalCmdItem cmdMoniteDislink = new ProtocalCmdItem(cmdDislink, false, 0);
            cmdMoniteDislink.recv = new byte[1024];

            if (bMoniting)
            {
                ProtocalCmd.commBlock(cmdMoniteDislink,false,1000);
            }

            if (!ProtocalCmd.bLink)
            {
                bMoniting = false;
                btn_connect.Text = "Connect";
                btn_connect.BackColor = Color.FromArgb(192, 79, 21);//黄色
                serialPort.Close();

                btn_Configuration.Visible = false;
                btn_Log.Visible = false;
                btn_TAL.Visible = false;
            }
        }

    }
    //读取控制器信息、软件版本、硬件版本。
    /*
        控制器信息cmd：[0xf0,0x03,0xfc]
        软件版本cmd：[0xf2,0x08,0xf7]
        硬件版本cmd：[0xf2,0x09,0xf6]
    */
    public void readVerInfo(byte target)
    {
        int len = 0;
        byte[] resbuff = new byte[1024];
        if (bMoniting)
        {
            // if (ProtocalCmd.bOldViesion)
            // {
            //     SysConfig.bTargetLinked = SysConfig.TRG_LINKED_SLAVE;
            // }
            // else
            // {
            //     //控制器
            //     byte[] cmdstruct = { 0xf0, 0x03, 0xfc };
            //     ProtocalCmdItem cmdMoniteStruct = new ProtocalCmdItem(cmdstruct, false, target);
            //     bool resStruct = ProtocalCmd.ReadInfo(cmdMoniteStruct, cmdstruct, resbuff, false, len, 0);
            //     if (resStruct)
            //     {
            //         Logger.Info("控制器发指令送成功");
            //     }
            //     Thread.Sleep(100);
            //     SysConfig.bTargetLinked = resbuff[0];
            //     SysConfig.bTargetStruct = resbuff[1];
            // }
            // Thread.Sleep(100);
            // //如果连接的是主控，读取主控的软硬件参数********************************************************************************************
            // if (SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER || SysConfig.bTargetStruct == SysConfig.TRG_LINKED_MASTER)
            // {
            //     //硬件
            //     byte[] cmdHard1 = { 0xf2, 0x07, 0xf8 };
            //     ProtocalCmdItem cmdMoniteHard1 = new ProtocalCmdItem(cmdHard1, false, target);
            //     bool resHard1 = ProtocalCmd.ReadInfo(cmdMoniteHard1, cmdHard1, resbuff, false, len, 0);
            //     if (resHard1)
            //     {
            //         Logger.Info("硬件版本号发指令送成功");
            //         lab_inverterModel.Text = ProtocalCmd.strHardVerMaster;
            //         Logger.Info($"硬件版本号:{ProtocalCmd.strHardVerMaster}");

            //     }
            //     Thread.Sleep(100);

            //     //软件
            //     byte[] cmdSoft1 = { 0xf2, 0x06, 0xf9 };
            //     ProtocalCmdItem cmdMoniteSoft1 = new ProtocalCmdItem(cmdSoft1, false, target);
            //     bool resSoft1 = ProtocalCmd.ReadInfo(cmdMoniteSoft1, cmdSoft1, resbuff, false, len, 0);
            //     if (resSoft1)
            //     {
            //         Logger.Info("软件版本号发指令送成功");
            //         lab_softwareVersion.Text = ProtocalCmd.strSoftVerMaster;
            //         Logger.Info($"软件版本号:{ProtocalCmd.strSoftVerMaster}");

            //     }
            // }

            // 读取驱动芯片信息*****************************************************************************************************************          

            bool needTrans = (SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER);
            //硬件
            byte[] cmdHard = { 0xf2, 0x07, 0xf8 };
            ProtocalCmdItem cmdMoniteHard = new ProtocalCmdItem(cmdHard, needTrans, target);
            bool resHard = ProtocalCmd.ReadInfo(cmdMoniteHard, cmdHard, resbuff, needTrans, len, 1);
            if (resHard)
            {
                Logger.Info("硬件版本号发指令送成功");
                lab_inverterModel.Text = ProtocalCmd.strHardVerMaster;
                Logger.Info($"硬件版本号:{ProtocalCmd.strHardVerMaster}");

            }
            Thread.Sleep(100);

            //软件
            byte[] cmdSoft = { 0xf2, 0x06, 0xf9 };
            ProtocalCmdItem cmdMoniteSoft = new ProtocalCmdItem(cmdSoft, needTrans, target);
            bool resSoft = ProtocalCmd.ReadInfo(cmdMoniteSoft, cmdSoft, resbuff, needTrans, len, 1);
            if (resSoft)
            {
                Logger.Info("软件版本号发指令送成功");
                lab_softwareVersion.Text = ProtocalCmd.strSoftVerMaster;
                Logger.Info($"软件版本号:{ProtocalCmd.strSoftVerMaster}");
            }

        }
    }

    // public void MainForm_Closed([NotNull]object sender, FormClosingEventArgs e)
    // {
    //     var form = (Form)sender;
    //     this.Close();
    // }
}
