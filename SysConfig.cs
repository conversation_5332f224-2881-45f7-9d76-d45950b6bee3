using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO.Ports;
using System.Linq;
using System.Threading.Tasks;

namespace CCTS
{
    public static class SysConfig
    {
        public static int iBps = 9600;
        public static int iParity = 2;
        public static int iDatabit = 8;
        public static int iStopbit = 1;
        public static string? strPort = null;
        public static string strDataPath = "./datas";
        public static string[] portNames = SerialPort.GetPortNames();
        public static int iReadTimeout = 2000;  //读超时
        public static int iMoniteInterval = 2000;  //监控周期间隔。监控任务启动后，每次任务执行完成后，间隔此时间后执行下一次任务。

        //控制器类型
        public static byte TRG_LINKED_MASTER = 0x10;//主控芯片
        public static byte TRG_LINKED_SLAVE = 0x01;//驱动芯片
        public static byte TRG_STRUCT_S1 = 0x01;//仅驱动芯片
        public static byte TRG_STRUCT_M1S1 = 0x11;//主控+1个驱动
        public static byte TRG_STRUCT_M1S2 = 0x12;//主控+2个驱动
        public static byte bTargetLinked, bTargetStruct = new byte();

        public static void loadParam()
        {
            Configuration config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
            if (config.AppSettings.Settings["dataPath"] != null)
            {
                strDataPath = config.AppSettings.Settings["dataPath"].Value;
                if (!Directory.Exists(strDataPath))
                    strDataPath = "./datas";
            }
            if (!Directory.Exists(strDataPath))
                Directory.CreateDirectory(strDataPath);


            if (config.AppSettings.Settings["bps"] != null)
                iBps = int.Parse(config.AppSettings.Settings["bps"].Value);

            if (config.AppSettings.Settings["parity"] != null)
                iParity = int.Parse(config.AppSettings.Settings["parity"].Value);

            if (config.AppSettings.Settings["databit"] != null)
                iDatabit = int.Parse(config.AppSettings.Settings["databit"].Value);

            if (config.AppSettings.Settings["stopbit"] != null)
                iStopbit = int.Parse(config.AppSettings.Settings["stopbit"].Value);

            if (config.AppSettings.Settings["comm"] != null)
                strPort = config.AppSettings.Settings["comm"].Value;

            config.Save(ConfigurationSaveMode.Modified);
            ConfigurationManager.RefreshSection("appSettings");
        }

        //保存串口设置
        public static void saveComm()
        {
            Configuration config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);

            if (config.AppSettings.Settings["bps"] == null)
                config.AppSettings.Settings.Add("bps", "" + iBps);
            else
                config.AppSettings.Settings["bps"].Value = "" + iBps;

            if (config.AppSettings.Settings["parity"] == null)
                config.AppSettings.Settings.Add("parity", "" + iParity);
            else
                config.AppSettings.Settings["parity"].Value = "" + iParity;

            if (config.AppSettings.Settings["databit"] == null)
                config.AppSettings.Settings.Add("databit", "" + iDatabit);
            else
                config.AppSettings.Settings["databit"].Value = "" + iDatabit;

            if (config.AppSettings.Settings["stopbit"] == null)
                config.AppSettings.Settings.Add("stopbit", "" + iStopbit);
            else
                config.AppSettings.Settings["stopbit"].Value = "" + iStopbit;

            if (strPort != null)
                if (config.AppSettings.Settings["comm"] == null)
                    config.AppSettings.Settings.Add("comm", "" + strPort);
                else
                    config.AppSettings.Settings["comm"].Value = "" + strPort;

            //保存配置文件
            config.Save(ConfigurationSaveMode.Modified);
            ConfigurationManager.RefreshSection("appSetting");

        }
          public static void saveDataSetting()
        {
            // 保存串口设置
            Configuration config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);


            if (config.AppSettings.Settings["dataPath"] == null)
                config.AppSettings.Settings.Add("dataPath", "" + strDataPath);
            else
                config.AppSettings.Settings["dataPath"].Value = "" + strDataPath;


            if (config.AppSettings.Settings["moniteInterval"] == null)
                config.AppSettings.Settings.Add("moniteInterval", "" + iMoniteInterval);
            else
                config.AppSettings.Settings["moniteInterval"].Value = "" + iMoniteInterval;
         
            
            // 保存配置文件
            config.Save(ConfigurationSaveMode.Modified);
            ConfigurationManager.RefreshSection("appSettings");
        }
        
    }
}