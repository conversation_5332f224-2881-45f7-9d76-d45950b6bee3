using Microsoft.Win32.SafeHandles;

namespace CCTS;

public partial class PopFormParametersLog : Form
{
    private MainForm mainForm;
    public DoubleBufferedDataGridView dgParam = new DoubleBufferedDataGridView();
    public DoubleBufferedDataGridView dgInverter = new DoubleBufferedDataGridView();
    public string[][] colText_Param =
    {
        new string[] {"Current speed", "Comp. Power", "Inv. Out. Current", "Bus Voltage", "Mcu Temp", "Inv. Temperature","Starting Trials"},
        new string[] {"RPM  (Compressor current speed)",  "W  (Inverter output power(compressor))", "Apk  (Inverter out current)",
            "Vdc  (Inverter bus voltage)", "℃  (Microcontroller temperature)", "℃  (Inverter internal temperature)",
            "(Compressor starting trials)"
        }
    };
    public string[] colText_inverter = { "Running", "Stopped", "Over Voltage Fault", "Under Voltage Fault", "Self Test Fault",
        "Startup Fault", "Hardware Overcurrent Fault","Software Overcurrent Fault", "Overpower Fault", "Stall Fault",
        "Over Temperature Fault",  "Phase Loss Fault"};
    int[] colWidth_dgParam = { 215, 200, 480 };
    int[] colWidth_dgInverter = { 50, 450 };
    short index;
    //int[] paramNum = { 2, 9, 4, 11, 16, 5, 6 };
    int[] paramNum = { 1, 8, 3, 10, 15, 4, 5 };
    //int[] paramNum = { 2, 1,3,0,4, 6,5 };
    public System.Threading.Timer timerParamMonite = null;

   
  
    public PopFormParametersLog(MainForm mainForm)
    {
        InitializeComponent();
        this.mainForm = mainForm;
        setGrid0();
        setGrid1();

        text_local.Text = SysConfig.strDataPath;

        timerParamMonite = new System.Threading.Timer(paramMoniting, null, Timeout.Infinite, Timeout.Infinite);

    }

    private void setGrid0()
    {
        //设置grid样式
        dgParam.AllowUserToAddRows = false;
        dgParam.AllowUserToOrderColumns = false;
        dgParam.AllowUserToDeleteRows = false;
        dgParam.AllowUserToResizeRows = false;
        dgParam.AllowUserToResizeColumns = false;
        //dgParamRight.EnableHeadersVisualStyles = false;
        dgParam.RowHeadersVisible = false;
        dgParam.BorderStyle = BorderStyle.None;
        dgParam.RowHeadersVisible = false;
        dgParam.ColumnHeadersVisible = false;



        dgParam.BackgroundColor = Color.FromArgb(11, 48, 65);
        dgParam.GridColor = Color.FromArgb(11, 48, 65);
        dgParam.Font = new Font(dgParam.Font.FontFamily, 10);
        dgParam.Height = 550;
        dgParam.Width = 900;
        dgParam.Location = new Point(30, 50);
        dgParam.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
        panel_param.Controls.Add(dgParam);

        for (int i = 0; i < 3; i++)
        {
            DataGridViewColumn customColumn;
            customColumn = new DataGridViewTextBoxColumn();
            customColumn.Width = colWidth_dgParam[i];
            if (i == 0 || i == 2)
            {
                customColumn.ReadOnly = true;
                customColumn.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            }
            else if (i == 1)
                customColumn.ValueType = typeof(float); 

            dgParam.Columns.Add(customColumn);
        }
        //添加行
        for (int i = 0; i < colText_Param[0].Length; i++)
        {
            // 添加新行
            int rowIndex = dgParam.Rows.Add();
            dgParam.Rows[rowIndex].Cells[0].Value = colText_Param[0][i];
            dgParam.Rows[rowIndex].Cells[2].Value = colText_Param[1][i];
            dgParam.Rows[rowIndex].Height = 70;
            dgParam.Rows[rowIndex].Cells[1].Style.BackColor = Color.White;
            dgParam.Rows[rowIndex].Cells[1].Style.ForeColor = Color.Black;
            //dgParam.Rows[rowIndex].Cells[1].Style.Padding = new Padding(0, 10, 0, 10);

            dgParam.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb(11, 48, 65);
            dgParam.Rows[rowIndex].DividerHeight = 15;

        }
    }
    private void setGrid1()
    {
        //设置grid样式
        dgInverter.AllowUserToAddRows = false;
        dgInverter.AllowUserToOrderColumns = false;
        dgInverter.AllowUserToDeleteRows = false;
        dgInverter.AllowUserToResizeRows = false;
        dgInverter.AllowUserToResizeColumns = false;
        //dgParamRight.EnableHeadersVisualStyles = false;
        dgInverter.RowHeadersVisible = false;
        dgInverter.BorderStyle = BorderStyle.None;
        dgInverter.RowHeadersVisible = false;
        dgInverter.ColumnHeadersVisible = false;



        dgInverter.BackgroundColor = Color.FromArgb(11, 48, 65);
        dgInverter.GridColor = Color.FromArgb(11, 48, 65);
        dgInverter.Font = new Font(dgParam.Font.FontFamily, 10);
        dgInverter.Height = 900;
        dgInverter.Width = 600;
        dgInverter.Location = new Point(100, label2.Height + 80);
        dgInverter.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleRight;
        panel_inverter.Controls.Add(dgInverter);

        for (int i = 0; i < 2; i++)
        {
            DataGridViewColumn customColumn;
            customColumn = new DataGridViewTextBoxColumn();
            customColumn.Width = colWidth_dgInverter[i];
            customColumn.ReadOnly = true;
            if (i == 1)
            {
                customColumn.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            }

            dgInverter.Columns.Add(customColumn);
        }
        //添加行
        for (int i = 0; i < colText_inverter.Length; i++)
        {
            // 添加新行
            int rowIndex = dgInverter.Rows.Add();
            dgInverter.Rows[rowIndex].Cells[1].Value = colText_inverter[i];
            dgInverter.Rows[rowIndex].Height = 70;
            dgInverter.Rows[rowIndex].Cells[0].Style.BackColor = Color.White;
            //dgParam.Rows[rowIndex].Cells[1].Style.ForeColor = Color.Black;
            //dgParam.Rows[rowIndex].Cells[1].Style.Padding = new Padding(0, 10, 0, 10);

            dgInverter.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb(11, 48, 65);
            dgInverter.Rows[rowIndex].DividerHeight = 15;

        }
    }
    //监控
    private void paramMoniting(object obj)
    {
        try
        {
            bool res = ProtocalCmd.ParamMoniting(SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);
            Task.Run(() =>
            {
                try
                {
                    if (res)
                    {
                        MainForm.Logger.Info("开始向表格内添加数值");
                        for (int rowIndex = 0; rowIndex < paramNum.Length; rowIndex++)
                        {
                            // 获取当前行对应的浮点数索引
                            int floatIndex = paramNum[rowIndex];
                            // 获取对应的浮点数值
                            float targetValue = ProtocalCmd.valueAll[floatIndex];
                            // 跨线程安全更新UI
                            dgParam.Invoke((MethodInvoker)delegate
                             {
                                 dgParam.Rows[rowIndex].Cells[1].Value = FormatFloatValue(targetValue);
                             });
                        }
                        MainForm.Logger.Info("表格内添加数值完成，开始更改错误表状态");
                        //运行或停止
                        if ((Int32)ProtocalCmd.valueAll[16] == 19)
                        {
                            dgInverter.Rows[0].Cells[0].Style.BackColor = Color.Green;
                            dgInverter.Rows[1].Cells[0].Style.BackColor = Color.White;
                        }
                        else
                        {
                            dgInverter.Rows[0].Cells[0].Style.BackColor = Color.White;
                            dgInverter.Rows[1].Cells[0].Style.BackColor = Color.Red;
                        }
                        //错误故障提示
                        for (int i = 2; i <= 11; i++)
                        {
                            dgInverter.Rows[i].Cells[0].Style.BackColor = Color.White;
                        }
                        //过压
                        if ((Int32)ProtocalCmd.valueAll[13] == 1)
                            dgInverter.Rows[2].Cells[0].Style.BackColor = Color.Red;
                        //欠压
                        else if ((Int32)ProtocalCmd.valueAll[13] == 2)
                            dgInverter.Rows[3].Cells[0].Style.BackColor = Color.Red;
                        //启动失败    
                        else if ((Int32)ProtocalCmd.valueAll[13] == 50 || (Int32)ProtocalCmd.valueAll[13] == 51 || (Int32)ProtocalCmd.valueAll[13] == 60 || (Int32)ProtocalCmd.valueAll[13] == 4)
                            dgInverter.Rows[5].Cells[0].Style.BackColor = Color.Red;
                        //硬件过流    
                        else if ((Int32)ProtocalCmd.valueAll[13] == 7 || (Int32)ProtocalCmd.valueAll[13] == 8)
                            dgInverter.Rows[6].Cells[0].Style.BackColor = Color.Red;
                        //软件过流    
                        else if ((Int32)ProtocalCmd.valueAll[13] == 3)
                            dgInverter.Rows[7].Cells[0].Style.BackColor = Color.Red;
                        //过载    
                        else if ((Int32)ProtocalCmd.valueAll[13] == 13 || (Int32)ProtocalCmd.valueAll[13] == 36)
                            dgInverter.Rows[8].Cells[0].Style.BackColor = Color.Red;
                        //失速    
                        else if ((Int32)ProtocalCmd.valueAll[13] == 34)
                            dgInverter.Rows[9].Cells[0].Style.BackColor = Color.Red;
                        //过温    
                        else if ((Int32)ProtocalCmd.valueAll[13] == 20)
                            dgInverter.Rows[10].Cells[0].Style.BackColor = Color.Red;
                        //缺相    
                        else if ((Int32)ProtocalCmd.valueAll[13] == 6)
                            dgInverter.Rows[11].Cells[0].Style.BackColor = Color.Red;
                        //自检故障
                        else if ((Int32)ProtocalCmd.valueAll[13] == 15 || (Int32)ProtocalCmd.valueAll[13] != 0)
                            dgInverter.Rows[4].Cells[0].Style.BackColor = Color.Red;
                        MainForm.Logger.Info("错误表状态完成");

                    }
                }
                catch (Exception ex)
                {
                    MainForm.Logger.Error(ex.Message);
                    MainForm.Logger.Error(ex.StackTrace);
                }
            });
        }
        catch (Exception ex)
        {
            MainForm.Logger.Error(ex.Message);
            MainForm.Logger.Error(ex.StackTrace);
        }
        if (mainForm.bMoniting && mainForm.bLog)
        {
            timerParamMonite.Change(SysConfig.iMoniteInterval, Timeout.Infinite);
        }

    }
    //读取转速
    private void btn_read_Click(object sender, EventArgs e)
    {
        Task.Run(() =>
       {
           bool res = ProtocalCmd.ReadComSpeed(SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);
           if (res)
           {

               dgv_compressor.Invoke((MethodInvoker)delegate
                {
                    //int rowId = dgv_compressor.Rows.Add();
                    dgv_compressor.Rows[0].Cells[0].Value = FormatFloatValue(ProtocalCmd.val);
                });
           }
       });

    }
    //写入转速
    private void btn_write_Click(object sender, EventArgs e)
    {
        Task.Run(() =>
       {
           if (dgv_compressor == null || dgv_compressor == null || dgv_compressor.Rows.Count == 0 || dgv_compressor.Rows.Count == 0)
           {
               MainForm.Logger.Warn("数据表格未初始化或位空");
               return;
           }

           float value = 0;

           bool parseSuccess = float.TryParse(dgv_compressor.Rows[0].Cells[0].Value.ToString(), out value);
           if (!parseSuccess)
           {
               MainForm.Logger.Error($"第{0}行数据转换失败：{dgv_compressor.Rows[0].Cells[0].Value}");
           }

           bool res = ProtocalCmd.WriteComSpeed(value, SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);

           if (res)
           {
               MainForm.Logger.Info($"成功写入行[{0}]:值={value}");
           }
           else
           {
               MainForm.Logger.Error($"写入行[{0}]失败:值={value}");
           }

       });

    }
    //启动压缩机
    private void btn_startCom_Click(object sender, EventArgs e)
    {
        Task.Run(() =>
        {
            bool res = ProtocalCmd.RunorStop(SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, true, 1);
            if (res)
            {
                MainForm.Logger.Info("启动压缩机指令发出成功");
            }
            else
                MainForm.Logger.Info("启动压缩机指令发出失败");
        });
    }
    //压缩机停止
    private void btn_stopCom_Click(object sender, EventArgs e)
    {
        Task.Run(() =>
        {
            bool res = ProtocalCmd.RunorStop(SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, false, 1);
            if (res)
            {
                MainForm.Logger.Info("启动压缩机指令发出成功");
            }
            else
                MainForm.Logger.Info("启动压缩机指令发出失败");
        });
    }
    //日志开始保存
    private void lab_local_Click(object sender, EventArgs e)
    {
        var dlg = new OpenFileDialog
        {
            ValidateNames = false,
            CheckFileExists = false,
            FileName = "选择文件夹"
        };
        if (dlg.ShowDialog() == DialogResult.OK)
            text_local.Text = dlg.FileName.Substring(0, dlg.FileName.LastIndexOf("\\"));
                
        SysConfig.iMoniteInterval = int.Parse(numRecInterval.Value.ToString()) * 1000;
    }

    private void btn_startLog_Click(object sender, EventArgs e)
    {
        ProtocalCmd.writeLog = true;
        ProtocalCmd.creatFile();      
        SysConfig.saveDataSetting();        
    }
    //日志停止保存
    private void btn_stopLog_Click(object sender, EventArgs e)
    {
        ProtocalCmd.writeLog = false;
    }
    private string FormatFloatValue(float value)
    {
        // 基本格式化，保留12位小数
        string result = value.ToString("F12");

        // 处理小数点后多余的零
        if (result.Contains('.'))
        {
            result = result.TrimEnd('0');
            if (result.EndsWith("."))
            {
                result += "0";
            }
        }

        return result;
    }
    private void btn_Home_Click(object sender, EventArgs e)
    {
        //SingleForm.ShowForm<MainForm>();
        //timerParamMonite.Change(Timeout.Infinite, Timeout.Infinite);
        // 安全停止定时器
        // if (timerParamMonite != null)
        // {
            timerParamMonite.Change(Timeout.Infinite, Timeout.Infinite);
            mainForm.bLog = false;
            ProtocalCmd.writeLog = false;
        //     // 若后续不再使用则释放
        //     timerParamMonite.Dispose();
        //     timerParamMonite = null;
        // }
        this.Hide();
        mainForm.Show();
    }
    private void PopFormParametersLog_FormClosed(object sender, FormClosedEventArgs e)
    {
        
        //timerParamMonite.Change(Timeout.Infinite, Timeout.Infinite);
        // if (timerParamMonite != null)
        // {
            timerParamMonite.Change(Timeout.Infinite, Timeout.Infinite);
            mainForm.bLog = false;
            ProtocalCmd.writeLog = false;
        //     // 若后续不再使用则释放
        //     timerParamMonite.Dispose();
        //     timerParamMonite = null;
        // }

        this.Hide();
        mainForm.Show();
        //SingleForm.ShowForm<MainForm>();
    }
}
