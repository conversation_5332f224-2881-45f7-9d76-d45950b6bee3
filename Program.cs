using System;
using System.IO;
using System.Windows.Forms;

namespace CCTS;

static class Program
{
     //private static MainForm? _mainForm;
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main()
    {
        SysConfig.loadParam();

        // 检查文件夹是否已经存在
        if (!Directory.Exists(SysConfig.strDataPath))
            Directory.CreateDirectory(SysConfig.strDataPath);
            
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);
        ApplicationConfiguration.Initialize();
        Application.Run(new MainForm());
        //  _mainForm = SingleForm.GetForm<MainForm>();
        // Application.Run(_mainForm);
    }    
}