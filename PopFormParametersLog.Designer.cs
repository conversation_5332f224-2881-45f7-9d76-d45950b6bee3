﻿namespace CCTS;

partial class PopFormParametersLog

{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }


    #region Windows Form Designer generated code
    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// 2025-06-05
    /// </summary>
    private void InitializeComponent()
    {

        this.components = new System.ComponentModel.Container();
        this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        this.ClientSize = new System.Drawing.Size(1800, 1430);
        this.Text = "Parameters Log";
         this.Icon = new System.Drawing.Icon("Resources/CCTS.ico");
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(78, 149, 217);
        this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.PopFormParametersLog_FormClosed);
        //
        //lable1
        //
        this.label1 = new Label();
        this.label1.Text = "PARAMETERS LOG";
        this.label1.Size = new Size(600, 100);
        this.label1.Anchor = AnchorStyles.None;
        this.label1.Location = new Point((this.ClientSize.Width - label1.Width) / 2, 40);
        this.label1.Font = new Font(label1.Font.FontFamily, 24, FontStyle.Bold);
        this.label1.ForeColor = Color.White;
        this.Controls.Add(label1);
        //
        //panel_param
        //
        this.panel_param = new CCTS.RoundcornerPanel();
        this.panel_param.Size = new Size(970, 570);
        this.panel_param.BackColor = Color.FromArgb(11, 48, 65);
        this.panel_param.ForeColor = Color.White;
        this.panel_param.Location = new Point(80, label1.Height + 60);
        this.Controls.Add(this.panel_param);
        //
        //panel_inverter
        //
        this.panel_inverter = new CCTS.RoundcornerPanel();
        this.panel_inverter.Size = new Size(600, 1140);
        this.panel_inverter.BackColor = Color.FromArgb(11, 48, 65);
        this.panel_inverter.ForeColor = Color.White;
        this.panel_inverter.Location = new Point(panel_param.Width + 150, label1.Height + 60);
        this.Controls.Add(this.panel_inverter);
        //
        //label2
        //
        this.label2 = new Label();
        this.label2.Text = "INVERTER OPERATION STATUS";
        this.label2.Size = new Size(480, 50);
        this.label2.Anchor = AnchorStyles.None;
        this.label2.Location = new Point((this.panel_inverter.Width - this.label2.Width) / 2, 40);
        this.label2.Font = new Font(label2.Font.FontFamily, 12, FontStyle.Bold);
        this.label2.ForeColor = Color.White;
        this.panel_inverter.Controls.Add(label2);
        //原界面设计
        #region 
        // //
        // //panel_compressor
        // //
        // this.panel_compressor = new CCTS.RoundcornerPanel();
        // this.panel_compressor.Size = new Size(550, 380);
        // this.panel_compressor.BackColor = Color.FromArgb(11, 48, 65);
        // this.panel_compressor.ForeColor = Color.White;
        // this.panel_compressor.Location = new Point(80, label1.Height + panel_param.Height + 130);
        // this.Controls.Add(this.panel_compressor);
        // //
        // //label3
        // //
        // this.label3 = new Label();
        // this.label3.Text = "COMPRESSOR PILOT OPERATION";
        // this.label3.Size = new Size(500, 50);
        // this.label3.Anchor = AnchorStyles.None;
        // this.label3.Location = new Point((this.panel_compressor.Width - this.label3.Width) / 2, 30);
        // this.label3.Font = new Font(label3.Font.FontFamily, 12, FontStyle.Bold);
        // this.label3.ForeColor = Color.White;
        // this.panel_compressor.Controls.Add(label3);
        // //
        // //label_compressor
        // //
        // this.label_compressor = new Label();
        // this.label_compressor.Text = "Compressor\nSpeed Setpoint";
        // this.label_compressor.Size = new Size(240, 80);
        // this.label_compressor.Anchor = AnchorStyles.None;
        // this.label_compressor.TextAlign = ContentAlignment.MiddleCenter;
        // this.label_compressor.Location = new Point(30, label3.Height + 40);
        // this.label_compressor.Font = new Font(this.label_compressor.Font.FontFamily, 12);
        // this.label_compressor.ForeColor = Color.White;
        // this.panel_compressor.Controls.Add(label_compressor);
        // //
        // //dgv_compressor
        // //
        // this.dgv_compressor = new CCTS.SingleDataGridView();
        // this.dgv_compressor.Rows[0].Height = 70;
        // this.dgv_compressor.Columns[0].Width = 230;
        // this.dgv_compressor.Location = new Point(30, this.label3.Height + this.label_compressor.Height + 45);
        // this.panel_compressor.Controls.Add(this.dgv_compressor);
        // //
        // //btn_write
        // //
        // this.btn_write = new CCTS.Roundcornerbtn();
        // this.btn_write.Text = "Write";
        // this.btn_write.Size = new Size(100, 60);
        // this.btn_write.Font = new Font(this.btn_write.Font.FontFamily, 10);
        // this.btn_write.BackColor = Color.FromArgb(47, 126, 39);//绿色
        // this.btn_write.FlatStyle = FlatStyle.Flat;
        // this.btn_write.FlatAppearance.BorderSize = 0;
        // this.btn_write.Location = new Point(30, this.label3.Height + this.label_compressor.Height + this.dgv_compressor.Height + 60);
        // this.btn_write.Click += new System.EventHandler(this.btn_write_Click);
        // this.panel_compressor.Controls.Add(this.btn_write);
        // //
        // //btn_read
        // //
        // this.btn_read = new CCTS.Roundcornerbtn();
        // this.btn_read.Text = "Read";
        // this.btn_read.Size = new Size(100, 60);
        // this.btn_read.Font = new Font(this.btn_read.Font.FontFamily, 10);
        // this.btn_read.BackColor = Color.FromArgb(233, 113, 50);//黄色
        // this.btn_read.FlatStyle = FlatStyle.Flat;
        // this.btn_read.FlatAppearance.BorderSize = 0;
        // this.btn_read.Location = new Point(this.btn_write.Width + 60, this.label3.Height + this.label_compressor.Height +
        //                                     this.dgv_compressor.Height + 60);
        // this.btn_read.Click += new System.EventHandler(this.btn_read_Click);
        // this.panel_compressor.Controls.Add(this.btn_read);
        // //
        // //btn_startCom
        // //
        // this.btn_startCom = new CCTS.Roundcornerbtn();
        // this.btn_startCom.Text = "START";
        // this.btn_startCom.Size = new Size(180, 90);
        // this.btn_startCom.Font = new Font(this.btn_startCom.Font.FontFamily, 12);
        // this.btn_startCom.BackColor = Color.FromArgb(47, 126, 39);//绿色
        // this.btn_startCom.FlatStyle = FlatStyle.Flat;
        // this.btn_startCom.FlatAppearance.BorderSize = 0;
        // this.btn_startCom.Location = new Point(this.label_compressor.Width + 70, label3.Height + 40);
        // this.btn_startCom.Click += new System.EventHandler(this.btn_startCom_Click);
        // this.panel_compressor.Controls.Add(this.btn_startCom);
        // //
        // //btn_stopCom
        // //
        // this.btn_stopCom = new CCTS.Roundcornerbtn();
        // this.btn_stopCom.Text = "STOP";
        // this.btn_stopCom.Size = new Size(180, 90);
        // this.btn_stopCom.Font = new Font(this.btn_stopCom.Font.FontFamily, 12);
        // this.btn_stopCom.BackColor = Color.FromArgb(255, 0, 0);//红色
        // this.btn_stopCom.FlatStyle = FlatStyle.Flat;
        // this.btn_stopCom.FlatAppearance.BorderSize = 0;
        // this.btn_stopCom.Location = new Point(this.label_compressor.Width + 70, this.label3.Height + this.btn_startCom.Height + 90);
        // this.btn_stopCom.Click += new System.EventHandler(this.btn_stopCom_Click);
        // this.panel_compressor.Controls.Add(this.btn_stopCom);
        // //
        // //panel_log
        // //
        // this.panel_log = new CCTS.RoundcornerPanel();
        // this.panel_log.Size = new Size(400, 380);
        // this.panel_log.BackColor = Color.FromArgb(11, 48, 65);
        // this.panel_log.ForeColor = Color.White;
        // this.panel_log.Location = new Point(panel_compressor.Width + 100, label1.Height + panel_param.Height + 130);
        // this.Controls.Add(this.panel_log);
        // //
        // //label4
        // //
        // this.label4 = new Label();
        // this.label4.Text = "LOG SAVE";
        // this.label4.Size = new Size(160, 50);
        // this.label4.Anchor = AnchorStyles.None;
        // this.label4.Location = new Point((this.panel_log.Width - this.label4.Width) / 2, 30);
        // this.label4.Font = new Font(label4.Font.FontFamily, 12, FontStyle.Bold);
        // this.label4.ForeColor = Color.White;
        // this.panel_log.Controls.Add(label4);
        // //
        // //lab_local
        // //
        // this.lab_local = new Label();
        // this.lab_local.Text = "Local:";
        // this.lab_local.Size = new Size(80, 70);
        // this.lab_local.Anchor = AnchorStyles.None;
        // this.lab_local.TextAlign = ContentAlignment.MiddleCenter;
        // this.lab_local.Location = new Point(35, label4.Height + 40);
        // this.lab_local.Font = new Font(label4.Font.FontFamily, 10);
        // this.lab_local.ForeColor = Color.White;
        // this.lab_local.Click += new EventHandler(this.lab_local_Click);
        // this.panel_log.Controls.Add(lab_local);
        // //
        // //dgv_local
        // //
        // // this.dgv_local = new CCTS.SingleDataGridView();
        // // this.dgv_local.Rows[0].Height = 70;
        // // this.dgv_local.Columns[0].Width = 230;        
        // // this.dgv_local.Location = new Point(lab_local.Width + 20, label4.Height + 40);
        // // this.panel_log.Controls.Add(this.dgv_local);
        // //
        // //text_local
        // //
        // this.text_local = new TextBox();
        // this.text_local.Height = 70;
        // this.text_local.Width = 260;        
        // this.text_local.Location = new Point(lab_local.Width + 40, label4.Height + 60);
        // this.panel_log.Controls.Add(this.text_local);
        // //
        // //lab_saveInterval
        // //
        // this.lab_saveInterval = new Label();
        // this.lab_saveInterval.Text = "Save interval:";
        // this.lab_saveInterval.Size = new Size(180, 70);
        // this.lab_saveInterval.Anchor = AnchorStyles.None;
        // this.lab_saveInterval.TextAlign = ContentAlignment.MiddleCenter;
        // this.lab_saveInterval.Location = new Point(35, label4.Height + lab_local.Height + 55);
        // this.lab_saveInterval.Font = new Font(label4.Font.FontFamily, 10);
        // this.lab_saveInterval.ForeColor = Color.White;
        // this.panel_log.Controls.Add(lab_saveInterval);
        // // //
        // // //dgv_saveInterval
        // // //
        // // this.dgv_saveInterval = new CCTS.SingleDataGridView();
        // // this.dgv_saveInterval.Rows[0].Height = 70;
        // // this.dgv_saveInterval.Columns[0].Width = 130;
        // // this.dgv_saveInterval.Location = new Point(lab_saveInterval.Width + 20, label4.Height + lab_local.Height + 55);
        // // this.panel_log.Controls.Add(this.dgv_saveInterval);
        // //
        // //text_saveInv
        // //
        // this.text_saveInv = new TextBox();
        // this.text_saveInv.Height = 70;
        // this.text_saveInv.Width = 160;        
        // this.text_saveInv.Location = new Point(lab_saveInterval.Width + 20, label4.Height + lab_local.Height + 75);;
        // this.panel_log.Controls.Add(this.text_saveInv);
        // //
        // //btn_startLog
        // //
        // this.btn_startLog = new CCTS.Roundcornerbtn();
        // this.btn_startLog.Text = "Start";
        // this.btn_startLog.Size = new Size(100, 60);
        // this.btn_startLog.Font = new Font(this.btn_startLog.Font.FontFamily, 10);
        // this.btn_startLog.BackColor = Color.FromArgb(47, 126, 39);//绿色
        // this.btn_startLog.FlatStyle = FlatStyle.Flat;
        // this.btn_startLog.FlatAppearance.BorderSize = 0;
        // this.btn_startLog.Location = new Point(60, lab_local.Height + lab_saveInterval.Height + 125);
        // this.btn_startLog.Click += new System.EventHandler(this.btn_startLog_Click);
        // this.panel_log.Controls.Add(this.btn_startLog);
        // //
        // //btn_stopLog
        // //
        // this.btn_stopLog = new CCTS.Roundcornerbtn();
        // this.btn_stopLog.Text = "Stop";
        // this.btn_stopLog.Size = new Size(100, 60);
        // this.btn_stopLog.Font = new Font(this.btn_read.Font.FontFamily, 10);
        // this.btn_stopLog.BackColor = Color.FromArgb(255, 0, 0);//红色
        // this.btn_stopLog.FlatStyle = FlatStyle.Flat;
        // this.btn_stopLog.FlatAppearance.BorderSize = 0;
        // this.btn_stopLog.Location = new Point(this.btn_startLog.Width + 100, lab_local.Height + lab_saveInterval.Height + 125);
        // this.btn_stopLog.Click += new System.EventHandler(this.btn_stopLog_Click);
        // this.panel_log.Controls.Add(this.btn_stopLog);
        #endregion
        //新界面设计************************************************************************************
        //
        //panel_compressor
        //
        this.panel_compressor = new CCTS.RoundcornerPanel();
        this.panel_compressor.Size = new Size(970, 250);
        this.panel_compressor.BackColor = Color.FromArgb(11, 48, 65);
        this.panel_compressor.ForeColor = Color.White;
        this.panel_compressor.Location = new Point(80, label1.Height + panel_param.Height + 90);
        this.Controls.Add(this.panel_compressor);
        //
        //label3
        //
        this.label3 = new Label();
        this.label3.Text = "COMPRESSOR PILOT OPERATION";
        this.label3.Size = new Size(500, 50);
        this.label3.Anchor = AnchorStyles.None;
        this.label3.Location = new Point((this.panel_compressor.Width - this.label3.Width) / 2, 15);
        this.label3.Font = new Font(label3.Font.FontFamily, 12, FontStyle.Bold);
        this.label3.ForeColor = Color.White;
        this.panel_compressor.Controls.Add(label3);
        //
        //label_compressor
        //
        this.lab_compressor = new Label();
        this.lab_compressor.Text = "Compressor Speed Setpoint :";
        this.lab_compressor.Size = new Size(400, 80);
        this.lab_compressor.Anchor = AnchorStyles.None;
        this.lab_compressor.TextAlign = ContentAlignment.MiddleCenter;
        this.lab_compressor.Location = new Point(20, label3.Height + 20);
        this.lab_compressor.Font = new Font(this.lab_compressor.Font.FontFamily, 11);
        this.lab_compressor.ForeColor = Color.White;
        this.panel_compressor.Controls.Add(lab_compressor);
        //
        //dgv_compressor
        //
        this.dgv_compressor = new CCTS.SingleDataGridView();
        this.dgv_compressor.Rows[0].Height = 70;
        this.dgv_compressor.Columns[0].Width = 230;
        this.dgv_compressor.Location = new Point(lab_compressor.Width + 20, this.label3.Height + 30);
        this.panel_compressor.Controls.Add(this.dgv_compressor);
        //
        //lab_UnitRPM
        //
        this.lab_UnitRPM = new Label();     
        this.lab_UnitRPM.Text = "RPM";
        this.lab_UnitRPM.Size = new Size(80, 70);
        this.lab_UnitRPM.Anchor = AnchorStyles.None;
        this.lab_UnitRPM.TextAlign = ContentAlignment.MiddleCenter;
        this.lab_UnitRPM.Location = new Point(this.lab_compressor.Width + this.dgv_compressor.Width+10, label3.Height + 30);
        this.lab_UnitRPM.Font = new Font(this.lab_compressor.Font.FontFamily, 11);
        this.lab_UnitRPM.ForeColor = Color.White;
        this.panel_compressor.Controls.Add(lab_UnitRPM);
        //
        //btn_write
        //
        this.btn_write = new CCTS.Roundcornerbtn();
        this.btn_write.Text = "Write";
        this.btn_write.Size = new Size(100, 60);
        this.btn_write.Font = new Font(this.btn_write.Font.FontFamily, 10);
        this.btn_write.BackColor = Color.FromArgb(47, 126, 39);//绿色
        this.btn_write.FlatStyle = FlatStyle.Flat;
        this.btn_write.FlatAppearance.BorderSize = 0;
        this.btn_write.Location = new Point(190, this.label3.Height + this.lab_compressor.Height + 40);
        this.btn_write.Click += new System.EventHandler(this.btn_write_Click);
        this.panel_compressor.Controls.Add(this.btn_write);
        //
        //btn_read
        //
        this.btn_read = new CCTS.Roundcornerbtn();
        this.btn_read.Text = "Read";
        this.btn_read.Size = new Size(100, 60);
        this.btn_read.Font = new Font(this.btn_read.Font.FontFamily, 10);
        this.btn_read.BackColor = Color.FromArgb(233, 113, 50);//黄色
        this.btn_read.FlatStyle = FlatStyle.Flat;
        this.btn_read.FlatAppearance.BorderSize = 0;
        this.btn_read.Location = new Point(this.btn_write.Width + 350, this.label3.Height + this.lab_compressor.Height + 40);
        this.btn_read.Click += new System.EventHandler(this.btn_read_Click);
        this.panel_compressor.Controls.Add(this.btn_read);
        //
        //btn_startCom
        //
        this.btn_startCom = new CCTS.Roundcornerbtn();
        this.btn_startCom.Text = "START";
        this.btn_startCom.Size = new Size(160, 70);
        this.btn_startCom.Font = new Font(this.btn_startCom.Font.FontFamily, 12);
        this.btn_startCom.BackColor = Color.FromArgb(47, 126, 39);//绿色
        this.btn_startCom.FlatStyle = FlatStyle.Flat;
        this.btn_startCom.FlatAppearance.BorderSize = 0;
        this.btn_startCom.Location = new Point(this.lab_compressor.Width + this.dgv_compressor.Width+this.lab_UnitRPM.Width + 50, this.label3.Height + 20);
        this.btn_startCom.Click += new System.EventHandler(this.btn_startCom_Click);
        this.panel_compressor.Controls.Add(this.btn_startCom);
        //
        //btn_stopCom
        //
        this.btn_stopCom = new CCTS.Roundcornerbtn();
        this.btn_stopCom.Text = "STOP";
        this.btn_stopCom.Size = new Size(160, 70);
        this.btn_stopCom.Font = new Font(this.btn_stopCom.Font.FontFamily, 12);
        this.btn_stopCom.BackColor = Color.FromArgb(255, 0, 0);//红色
        this.btn_stopCom.FlatStyle = FlatStyle.Flat;
        this.btn_stopCom.FlatAppearance.BorderSize = 0;
        this.btn_stopCom.Location = new Point(this.lab_compressor.Width + this.dgv_compressor.Width+this.lab_UnitRPM.Width + 50, this.label3.Height + this.btn_startCom.Height + 40);
        this.btn_stopCom.Click += new System.EventHandler(this.btn_stopCom_Click);
        this.panel_compressor.Controls.Add(this.btn_stopCom);
        //
        //panel_log
        //
        this.panel_log = new CCTS.RoundcornerPanel();
        this.panel_log.Size = new Size(970, 260);
        this.panel_log.BackColor = Color.FromArgb(11, 48, 65);
        this.panel_log.ForeColor = Color.White;
        this.panel_log.Location = new Point(80, label1.Height + panel_param.Height + panel_compressor.Height + 120);
        this.Controls.Add(this.panel_log);
        //
        //label4
        //
        this.label4 = new Label();
        this.label4.Text = "LOG SAVE";
        this.label4.Size = new Size(160, 50);
        this.label4.Anchor = AnchorStyles.None;
        this.label4.Location = new Point((this.panel_log.Width - this.label4.Width) / 2, 20);
        this.label4.Font = new Font(label4.Font.FontFamily, 12, FontStyle.Bold);
        this.label4.ForeColor = Color.White;
        this.panel_log.Controls.Add(label4);
        //
        //lab_saveInterval
        //
        this.lab_saveInterval = new Label();
        this.lab_saveInterval.Text = "Save interval:";
        this.lab_saveInterval.Size = new Size(200, 30);
        this.lab_saveInterval.Anchor = AnchorStyles.None;
        this.lab_saveInterval.TextAlign = ContentAlignment.MiddleCenter;
        this.lab_saveInterval.Location = new Point(50, label4.Height + 30);
        this.lab_saveInterval.Font = new Font(label4.Font.FontFamily, 11);
        this.lab_saveInterval.ForeColor = Color.White;
        this.panel_log.Controls.Add(lab_saveInterval);
        //
        //text_saveInv
        //
        this.numRecInterval = new NumericUpDown();
        this.numRecInterval.Text = "2";
        this.numRecInterval.Width = 200;
        this.numRecInterval.Location = new Point(lab_saveInterval.Width + 50, label4.Height + 30); ;
        this.panel_log.Controls.Add(this.numRecInterval);
        //
        //lab_UnitSec
        //
        this.lab_UnitSec = new Label();     
        this.lab_UnitSec.Text = "Sec";
        this.lab_UnitSec.Size = new Size(80, 50);
        this.lab_UnitSec.Anchor = AnchorStyles.None;
        this.lab_UnitSec.TextAlign = ContentAlignment.MiddleCenter;
        this.lab_UnitSec.Location = new Point(lab_saveInterval.Width + this.numRecInterval.Width + 45, label3.Height + 20);
        this.lab_UnitSec.Font = new Font(this.lab_compressor.Font.FontFamily, 11);
        this.lab_UnitSec.ForeColor = Color.White;
        this.panel_log.Controls.Add(lab_UnitSec);
        //
        //lab_local
        //
        this.lab_local = new Label();
        this.lab_local.Text = "Local:";
        this.lab_local.Size = new Size(100, 30);
        this.lab_local.Anchor = AnchorStyles.None;
        this.lab_local.TextAlign = ContentAlignment.MiddleCenter;
        this.lab_local.Location = new Point(50, label4.Height + lab_local.Height + 50);
        this.lab_local.Font = new Font(label4.Font.FontFamily, 12);
        this.lab_local.ForeColor = Color.White;
        this.lab_local.Click += new EventHandler(this.lab_local_Click);
        this.panel_log.Controls.Add(lab_local);
        //
        //text_local
        //
        this.text_local = new TextBox();
        this.text_local.Width = 750;
        this.text_local.Location = new Point(lab_local.Width + 50, label4.Height + lab_local.Height + 50);
        this.panel_log.Controls.Add(this.text_local);
        //
        //btn_startLog
        //
        this.btn_startLog = new CCTS.Roundcornerbtn();
        this.btn_startLog.Text = "Start";
        this.btn_startLog.Size = new Size(100, 60);
        this.btn_startLog.Font = new Font(this.btn_startLog.Font.FontFamily, 10);
        this.btn_startLog.BackColor = Color.FromArgb(47, 126, 39);//绿色
        this.btn_startLog.FlatStyle = FlatStyle.Flat;
        this.btn_startLog.FlatAppearance.BorderSize = 0;
        this.btn_startLog.Location = new Point(300, lab_local.Height + lab_saveInterval.Height + label4.Height + 70);
        this.btn_startLog.Click += new System.EventHandler(this.btn_startLog_Click);
        this.panel_log.Controls.Add(this.btn_startLog);
        //
        //btn_stopLog
        //
        this.btn_stopLog = new CCTS.Roundcornerbtn();
        this.btn_stopLog.Text = "Stop";
        this.btn_stopLog.Size = new Size(100, 60);
        this.btn_stopLog.Font = new Font(this.btn_read.Font.FontFamily, 10);
        this.btn_stopLog.BackColor = Color.FromArgb(255, 0, 0);//红色
        this.btn_stopLog.FlatStyle = FlatStyle.Flat;
        this.btn_stopLog.FlatAppearance.BorderSize = 0;
        this.btn_stopLog.Location = new Point(this.btn_startLog.Width + 450, lab_local.Height + lab_saveInterval.Height + label4.Height + 70);
        this.btn_stopLog.Click += new System.EventHandler(this.btn_stopLog_Click);
        this.panel_log.Controls.Add(this.btn_stopLog);
        //
        //btn_Home
        //
        this.btn_Home = new CCTS.Roundcornerbtn();
        this.btn_Home.Text = "Home";
        this.btn_Home.Size = new Size(150, 80);
        this.btn_Home.Location = new Point(100, 1320);
        this.btn_Home.ForeColor = Color.White;
        this.btn_Home.Font = new Font(this.btn_Home.Font.FontFamily, 12);
        this.btn_Home.BackColor = Color.FromArgb(11, 48, 65);
        this.btn_Home.FlatStyle = FlatStyle.Flat;
        this.btn_Home.FlatAppearance.BorderSize = 0;
        this.btn_Home.Click += new EventHandler(this.btn_Home_Click);
        this.Controls.Add(this.btn_Home);






    }

    #endregion
    private System.Windows.Forms.Label label1, label2, label3, lab_compressor, label4, lab_local, lab_saveInterval,lab_UnitRPM,lab_UnitSec;
    private System.Windows.Forms.Panel panel_param, panel_inverter, panel_compressor, panel_log;
    private System.Windows.Forms.DataGridView dgv_compressor, dgv_local, dgv_saveInterval;
    private System.Windows.Forms.Button btn_write, btn_read, btn_startCom, btn_stopCom, btn_startLog, btn_stopLog, btn_Home;
    private System.Windows.Forms.TextBox text_local;
    private System.Windows.Forms.NumericUpDown numRecInterval;
}

